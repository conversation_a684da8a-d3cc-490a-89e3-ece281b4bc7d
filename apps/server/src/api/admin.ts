/* eslint-disable @typescript-eslint/no-explicit-any */
import express from "express";
import { adminAuthenticate, requireAdmin } from "./middleware/admin-auth";
import { validateRequest } from "./middleware/validation";
import { NotFoundError } from "./middleware/error-handler";
import { agencyService } from "../services/agency-service";
import { userService } from "../services/user-service";
import { schemaService } from "../services/schema-service";
import { resourceService } from "../services/resource-service";
import { uiSchemaService } from "../services/ui-schema-service";
import { getSchemaResolverService } from "../services/schema-resolver-service";
import { stytchClient } from "../services/stytch-service";
import { getSignedUrl } from "../services/storage-service";
import { Request } from "@hospice-os/apptypes";
import {
  startPatientRequestWorkflow,
  startPdfImportWorkflow,
  getPdfImportWorkflowHandle,
  getWorkflowId,
  signalPatientRequestWorkflowSafely,
} from "../temporal/client";
import {
  convertPDFToMarkdownSignal,
  processSubdocumentsSignal,
  createResourcesForSubdocumentsSignal,
} from "../temporal/pdf/pdfWorkflow";

const router = express.Router();

// Apply admin authentication to all admin routes
router.use(adminAuthenticate);
router.use(requireAdmin);

// Admin-specific routes

/**
 * GET /api/admin/agencies
 * List all agencies
 */
router.get("/agencies", async (req, res, next) => {
  try {
    const agencies = await agencyService.listAgencies();
    res.json(agencies);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/agencies/:id
 * Get an agency by ID
 */
router.get("/agencies/:id", async (req, res, next) => {
  try {
    const agency = await agencyService.getAgency(req.params.id);

    if (!agency) {
      throw new NotFoundError(`Agency with ID ${req.params.id} not found`);
    }

    res.json(agency);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/agencies
 * Create a new agency
 */
router.post(
  "/agencies",
  validateRequest("createAgency"),
  async (req, res, next) => {
    try {
      const { name, stytchOrganizationId } = req.body;

      const newAgency = await agencyService.createAgency(
        name,
        stytchOrganizationId,
      );

      res.status(201).json(newAgency);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * PUT /api/admin/agencies/:id
 * Update an agency
 */
router.put(
  "/agencies/:id",
  validateRequest("updateAgency"),
  async (req, res, next) => {
    try {
      const { name, stytchOrganizationId } = req.body;

      const updatedAgency = await agencyService.updateAgency(
        req.params.id,
        name,
        stytchOrganizationId,
      );

      if (!updatedAgency) {
        throw new NotFoundError(`Agency with ID ${req.params.id} not found`);
      }

      res.json(updatedAgency);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * GET /api/admin/presigned-download-url?urlPath=/path/to/file.pdf
 * Get a presigned URL for a resource
 */
router.get("/presigned-download-url", async (req, res, next) => {
  try {
    const { urlPath } = req.query;
    console.log("urlPath", urlPath);
    const presignedUrl = await getSignedUrl(urlPath as string);
    console.log("presignedUrl", presignedUrl);
    res.json({ presignedUrl });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/users/role/:role
 * List users by role
 */
router.get("/users/role/:role", async (req, res, next) => {
  try {
    const { agencyId } = req.query;
    const role = req.params.role === "all" ? "all" : req.params.role;
    const users = await userService.queryUsersByType(
      role,
      agencyId !== "null" ? (agencyId as string) : req.agencyId,
    );

    res.json(users);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/users/:id
 * Get a user by ID
 */
router.get("/users/id/:id", async (req, res, next) => {
  try {
    const user = await userService.readUser(req.params.id);

    if (!user) {
      throw new NotFoundError(`User with ID ${req.params.id} not found`);
    }

    res.json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/users/stytch/:stytchId
 * Get a user by Stytch ID
 */
router.get("/users/stytch/:stytchId", async (req, res, next) => {
  try {
    const user = await userService.getUserByStytchId(req.params.stytchId);

    if (!user) {
      throw new NotFoundError(
        `User with Stytch ID ${req.params.stytchId} not found`,
      );
    }

    res.json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/users
 * Create a new user
 */
router.post("/users", validateRequest("createUser"), async (req, res, next) => {
  try {
    const { schemaId, agencyId, stytchId, ...userData } = req.body;

    const newUser = await userService.createUser(
      userData,
      schemaId,
      agencyId,
      stytchId,
    );

    const agency = await agencyService.getAgency(agencyId);

    const params = {
      organization_id: agency.stytchOrganizationId!,
      email_address: userData.email,
      external_id: newUser.id,
      name: userData.firstName + " " + userData.lastName,
    };

    const stytchResult =
      await stytchClient.organizations.members.create(params);

    await userService.updateUser(
      newUser.id,
      newUser,
      schemaId,
      stytchResult.member_id,
    );

    res.status(201).json(newUser);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/admin/users/:id
 * Update a user
 */
router.put(
  "/users/:id",
  validateRequest("updateUser"),
  async (req, res, next) => {
    try {
      const { schemaId, stytchId, ...userData } = req.body;

      const updatedUser = await userService.updateUser(
        req.params.id,
        userData,
        schemaId,
        stytchId,
      );

      if (!updatedUser) {
        throw new NotFoundError(`User with ID ${req.params.id} not found`);
      }

      res.json(updatedUser);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * GET /api/admin/resources/:type
 * List resources by type
 */
router.get("/resources/:type", async (req, res, next) => {
  try {
    const type = req.params.type === "all" ? undefined : req.params.type;
    const resources = type
      ? await resourceService.queryResourcesByType(type, req.agencyId)
      : await resourceService.queryResourcesByType("all", req.agencyId);

    res.json(resources);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/resources/filter
 * Filter resources by type and status
 */
router.get("/resources", async (req, res, next) => {
  try {
    const { type, status, agencyId } = req.query;
    const typeFilter = type === "all" ? undefined : (type as string);
    const statusFilter = status === "all" ? undefined : (status as string);
    const agencyIdFilter =
      agencyId === "all" ? undefined : (agencyId as string);

    // For now, we'll just use the existing queryResourcesByType method
    // In a real implementation, you would modify the resource service to support filtering by both type and status
    const resources = await resourceService.queryResourcesByType(
      typeFilter || statusFilter || "all",
      agencyIdFilter,
    );

    // If both filters are provided, we'll filter the results in memory
    // In a real implementation, this would be done at the database level
    let filteredResources = resources;

    if (typeFilter && statusFilter) {
      filteredResources = resources.filter(
        (resource: Record<string, unknown>) => resource.status === statusFilter,
      );
    } else if (statusFilter) {
      filteredResources = resources.filter(
        (resource: Record<string, unknown>) => resource.status === statusFilter,
      );
    }

    res.json(filteredResources);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/resources/id/:id
 * Get a resource by ID
 */
router.get("/resources/id/:id", async (req, res, next) => {
  try {
    const resource = await resourceService.readResource(req.params.id);

    if (!resource) {
      throw new NotFoundError(`Resource with ID ${req.params.id} not found`);
    }

    res.json(resource);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/schemas
 * List all schemas
 */
router.get("/schemas", async (req, res, next) => {
  try {
    const schemas = await schemaService.listAllSchemas();
    res.json(schemas);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/schemas/base
 * List all base schemas
 */
router.get("/schemas/base", async (req, res, next) => {
  try {
    const schemas = await schemaService.listBaseSchemas();
    res.json(schemas);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/schemas/:id
 * Get a schema by ID
 */
router.get("/schemas/:id", async (req, res, next) => {
  try {
    const schema = await schemaService.getSchema(req.params.id);

    if (!schema) {
      throw new NotFoundError(`Schema with ID ${req.params.id} not found`);
    }

    res.json(schema);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/schemas
 * Create a new schema
 */
router.post(
  "/schemas",
  validateRequest("createSchema"),
  async (req, res, next) => {
    try {
      const { name, version, agencyId, schema, baseSchemaId } = req.body;
      let agencyIdToUse = agencyId;
      if (!agencyId) {
        agencyIdToUse = req.agencyId;
      }
      console.log("agencyIdToUse", agencyIdToUse);
      const newSchema = await schemaService.createSchema(
        name,
        version,
        agencyIdToUse,
        schema,
        baseSchemaId,
      );

      res.status(201).json(newSchema);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * PUT /api/admin/schemas/:id
 * Update a schema
 */
router.put(
  "/schemas/:id",
  validateRequest("updateSchema"),
  async (req, res, next) => {
    try {
      const { version, schema } = req.body;

      const updatedSchema = await schemaService.updateSchema(
        req.params.id,
        version,
        schema,
      );

      if (!updatedSchema) {
        throw new NotFoundError(`Schema with ID ${req.params.id} not found`);
      }

      res.json(updatedSchema);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * GET /api/schemas/:id/resolved
 * Get a resolved schema by ID (resolves $refs, $allOf, $anyOf)
 */
router.get("/schemas/:id/resolved", async (req, res, next) => {
  try {
    // Create a new instance of the schema resolver service for this request
    const schemaResolver = getSchemaResolverService();
    const resolvedSchema = await schemaResolver.resolveSchema(req.params.id);

    if (!resolvedSchema) {
      throw new NotFoundError(`Schema with ID ${req.params.id} not found`);
    }

    res.json(resolvedSchema);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/schemas/validate
 * Validate data against a schema
 */
router.post(
  "/schemas/validate",
  validateRequest("validateAgainstSchema"),
  async (req, res, next) => {
    try {
      const { schemaId, data } = req.body;

      const result = await schemaService.validateAgainstSchema(schemaId, data);
      res.json(result);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * PUT /api/admin/resources/:id
 * Update a resource (for review/approval)
 */
router.put(
  "/resources/:id",
  validateRequest("updateResource"),
  async (req, res, next) => {
    try {
      const { schemaId, ...resourceData } = req.body;

      const updatedResource = await resourceService.updateResource(
        req.params.id,
        resourceData,
        schemaId,
      );

      if (!updatedResource) {
        throw new NotFoundError(`Resource with ID ${req.params.id} not found`);
      }

      res.json(updatedResource);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * POST /api/admin/resources
 * Create a new resource
 */
router.post(
  "/resources",
  validateRequest("createResource"),
  async (req, res, next) => {
    try {
      const { schemaId, agencyId, ...resourceData } = req.body;

      const newResource = await resourceService.createResource(
        resourceData,
        schemaId,
        agencyId,
      );

      res.status(201).json(newResource);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * DELETE /api/admin/resources/:id
 * Delete a resource
 */
router.delete("/resources/:id", async (req, res, next) => {
  try {
    const deleted = await resourceService.deleteResource(req.params.id);
    if (!deleted) {
      throw new NotFoundError(`Resource with ID ${req.params.id} not found`);
    }
    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/ui-schemas/schema/:schemaId
 * List all UI schemas for a schema
 */
router.get("/ui-schemas/schema/:schemaId", async (req, res, next) => {
  try {
    const uiSchemas = await uiSchemaService.listUISchemasBySchema(
      req.params.schemaId,
    );
    res.json(uiSchemas);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/admin/ui-schemas/:id
 * Get a UI schema by ID
 */
router.get("/ui-schemas/:id", async (req, res, next) => {
  try {
    const uiSchema = await uiSchemaService.getUISchema(req.params.id);

    if (!uiSchema) {
      throw new NotFoundError(`UI Schema with ID ${req.params.id} not found`);
    }

    res.json(uiSchema);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/ui-schemas
 * Create a new UI schema
 */
router.post(
  "/ui-schemas",
  validateRequest("createUISchema"),
  async (req, res, next) => {
    try {
      const { name, description, schemaId, content } = req.body;

      const newUISchema = await uiSchemaService.createUISchema(
        name,
        description || "",
        schemaId,
        content,
      );

      res.status(201).json(newUISchema);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * PUT /api/admin/ui-schemas/:id
 * Update a UI schema
 */
router.put(
  "/ui-schemas/:id",
  validateRequest("updateUISchema"),
  async (req, res, next) => {
    try {
      const { name, description, content } = req.body;

      const updatedUISchema = await uiSchemaService.updateUISchema(
        req.params.id,
        name,
        description || "",
        content,
      );

      if (!updatedUISchema) {
        throw new NotFoundError(`UI Schema with ID ${req.params.id} not found`);
      }

      res.json(updatedUISchema);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * DELETE /api/admin/ui-schemas/:id
 * Delete a UI schema
 */
router.delete("/ui-schemas/:id", async (req, res, next) => {
  try {
    const deleted = await uiSchemaService.deleteUISchema(req.params.id);

    if (!deleted) {
      throw new NotFoundError(`UI Schema with ID ${req.params.id} not found`);
    }

    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/requests/:id/reprocess
 * Re-run patient workflow processing for a request
 */
router.post("/requests/:id/reprocess", async (req, res, next) => {
  try {
    const request = await resourceService.readResource(req.params.id);

    if (!request || request.resourceType !== "Request") {
      throw new NotFoundError(`Request with ID ${req.params.id} not found`);
    }

    const handle = await startPatientRequestWorkflow(
      `reprocess-${Date.now()}-${req.params.id}`,
      request as unknown as Request,
    );

    res.json({ invocationId: handle.workflowId });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/requests/:id/copy-reprocess
 * Create a copy of a request and start processing
 */
router.post("/requests/:id/copy-reprocess", async (req, res, next) => {
  try {
    const request = (await resourceService.readResource(
      req.params.id,
    )) as Request;

    if (!request || request.resourceType !== "Request") {
      throw new NotFoundError(`Request with ID ${req.params.id} not found`);
    }

    const { schemaId, agencyId, ...rest } = request as any;

    const newRequest = await resourceService.createResource(
      { ...(rest as any), resourceType: "Request" },
      schemaId,
      agencyId,
    );

    const flowId = getWorkflowId({
      prefix: "patientRequest",
      agencyId: newRequest.agencyId,
      entityId: newRequest.id,
      reprocess: true,
    });

    const handle = await startPatientRequestWorkflow(
      flowId,
      newRequest as unknown as Request,
    );

    res
      .status(201)
      .json({ id: newRequest.id, invocationId: handle.workflowId });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/admin/requests/:id/review
 * Simulate review of a request
 */
router.post("/requests/:id/review", async (req, res, next) => {
  if (!req.user?.id) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  try {
    const result = await signalPatientRequestWorkflowSafely(
      req.params.id,
      req.agencyId,
      "review",
      {
        userId: req.user.id,
        requestId: req.params.id,
      },
    );

    if (!result.signalSent) {
      res.status(400).json({
        error: "Request is already completed, cannot review",
        requestId: req.params.id,
      });
      return;
    }

    res.json({
      invocationId: result.workflowId || req.params.id,
      workflowStarted: result.workflowStarted,
    });
  } catch (error) {
    next(error);
  }
});

router.post("/requests/:id/referral-pdf", async (req, res, next) => {
  try {
    const { pdfUrl } = req.body;
    // Start or get existing PDF workflow
    let handle;
    try {
      handle = await getPdfImportWorkflowHandle(req.params.id, req.agencyId);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      // Workflow doesn't exist, start a new one
      handle = await startPdfImportWorkflow(req.params.id, req.agencyId);
    }

    await handle.signal(convertPDFToMarkdownSignal, {
      requestId: req.params.id,
      pdfUrl,
    });

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

router.post("/requests/:id/generate-plan-of-care", async (req, res, next) => {
  try {
    // await restateClient
    //   .objectSendClient(patientImportPdfWorkflow, req.params.id)
    //   .generatePlanOfCare({ planOfCare: req.body });

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

router.post("/requests/:id/generate-patient-chart", async (req, res, next) => {
  try {
    // await restateClient
    //   .objectSendClient(patientImportPdfWorkflow, req.params.id)
    //   .generatePatientChart({ patientChart: req.body });

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

router.post("/requests/:id/classify-subdocuments", async (req, res, next) => {
  try {
    // await restateClient
    //   .objectSendClient(patientImportPdfWorkflow, req.params.id)
    //   .classifySubdocuments();

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

router.post("/requests/:id/process-subdocuments", async (req, res, next) => {
  try {
    const handle = await getPdfImportWorkflowHandle(
      req.params.id,
      req.agencyId,
    );
    await handle.signal(processSubdocumentsSignal, {
      agencyId: req.agencyId,
      patientId: req.params.id,
    });

    res.json({ invocationId: req.params.id, requestId: req.params.id });
  } catch (error) {
    next(error);
  }
});

router.post(
  "/requests/:id/create-resources-for-subdocuments",
  async (req, res, next) => {
    try {
      const handle = await getPdfImportWorkflowHandle(
        req.params.id,
        req.agencyId,
      );
      await handle.signal(createResourcesForSubdocumentsSignal, {
        agencyId: req.agencyId,
        patientId: req.params.id,
      });

      res.json({ invocationId: req.params.id, requestId: req.params.id });
    } catch (error) {
      next(error);
    }
  },
);

/**
 * POST /api/admin/requests/:id/process-note-schema
 * Process note schema for request
 */
router.post("/requests/:id/process-note-schema", async (req, res, next) => {
  try {
    const { noteSchemaId } = req.body;

    if (!noteSchemaId) {
      return res.status(400).json({ error: "noteSchemaId is required" });
    }

    const request = await resourceService.readResource(req.params.id);

    if (!request || request.resourceType !== "Request") {
      throw new NotFoundError(`Request with ID ${req.params.id} not found`);
    }

    const result = await signalPatientRequestWorkflowSafely(
      req.params.id,
      request.agencyId,
      "processNoteSchema",
      {
        requestId: req.params.id,
        noteSchemaId,
      },
    );

    if (!result.signalSent) {
      res.status(400).json({
        error: "Request is already completed, cannot process note schema",
        requestId: req.params.id,
      });
      return;
    }

    res.json({
      invocationId: result.workflowId || req.params.id,
      workflowStarted: result.workflowStarted,
    });
  } catch (error) {
    next(error);
  }
});

export default router;
