import { describe, it, expect, vi, beforeEach } from 'vitest';
import { signalPatientRequestWorkflowSafely } from '../client';
import { resourceService } from '../../services/resource-service';
import { WorkflowNotFoundError } from '@temporalio/client';

// Mock the dependencies
vi.mock('../../services/resource-service');
vi.mock('../client', async () => {
  const actual = await vi.importActual('../client');
  return {
    ...actual,
    getPatientRequestWorkflowHandle: vi.fn(),
    startPatientRequestWorkflow: vi.fn(),
    getWorkflowId: vi.fn(),
  };
});

const mockResourceService = vi.mocked(resourceService);
const mockGetPatientRequestWorkflowHandle = vi.fn();
const mockStartPatientRequestWorkflow = vi.fn();
const mockGetWorkflowId = vi.fn();

// Import the mocked functions
vi.doMock('../client', () => ({
  signalPatientRequestWorkflowSafely,
  getPatientRequestWorkflowHandle: mockGetPatientRequestWorkflowHandle,
  startPatientRequestWorkflow: mockStartPatientRequestWorkflow,
  getWorkflowId: mockGetWorkflowId,
}));

describe('signalPatientRequestWorkflowSafely', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should skip signal if request is completed', async () => {
    // Mock a completed request
    mockResourceService.readResource.mockResolvedValue({
      id: 'test-request-id',
      resourceType: 'Request',
      status: 'completed',
    });

    const result = await signalPatientRequestWorkflowSafely(
      'test-request-id',
      'test-agency-id',
      'review',
      { userId: 'test-user-id' }
    );

    expect(result).toEqual({
      signalSent: false,
      workflowStarted: false,
    });
    expect(mockGetPatientRequestWorkflowHandle).not.toHaveBeenCalled();
    expect(mockStartPatientRequestWorkflow).not.toHaveBeenCalled();
  });

  it('should send signal to existing running workflow', async () => {
    // Mock a non-completed request
    mockResourceService.readResource.mockResolvedValue({
      id: 'test-request-id',
      resourceType: 'Request',
      status: 'pending_review',
    });

    // Mock existing running workflow
    const mockHandle = {
      workflowId: 'test-workflow-id',
      describe: vi.fn().mockResolvedValue({ status: { name: 'RUNNING' } }),
      signal: vi.fn().mockResolvedValue(undefined),
    };
    mockGetPatientRequestWorkflowHandle.mockResolvedValue(mockHandle);

    const result = await signalPatientRequestWorkflowSafely(
      'test-request-id',
      'test-agency-id',
      'review',
      { userId: 'test-user-id' }
    );

    expect(result).toEqual({
      signalSent: true,
      workflowStarted: false,
      workflowId: 'test-workflow-id',
    });
    expect(mockHandle.signal).toHaveBeenCalledWith('review', { userId: 'test-user-id' });
    expect(mockStartPatientRequestWorkflow).not.toHaveBeenCalled();
  });

  it('should start new workflow when workflow not found', async () => {
    // Mock a non-completed request
    const mockRequest = {
      id: 'test-request-id',
      resourceType: 'Request',
      status: 'pending_review',
    };
    mockResourceService.readResource.mockResolvedValue(mockRequest);

    // Mock workflow not found
    mockGetPatientRequestWorkflowHandle.mockRejectedValue(new WorkflowNotFoundError('Workflow not found'));

    // Mock new workflow creation
    const mockNewHandle = {
      workflowId: 'new-workflow-id',
      signal: vi.fn().mockResolvedValue(undefined),
    };
    mockStartPatientRequestWorkflow.mockResolvedValue(mockNewHandle);
    mockGetWorkflowId.mockReturnValue('new-workflow-id');

    const result = await signalPatientRequestWorkflowSafely(
      'test-request-id',
      'test-agency-id',
      'review',
      { userId: 'test-user-id' }
    );

    expect(result).toEqual({
      signalSent: true,
      workflowStarted: true,
      workflowId: 'new-workflow-id',
    });
    expect(mockStartPatientRequestWorkflow).toHaveBeenCalledWith('new-workflow-id', mockRequest);
    expect(mockNewHandle.signal).toHaveBeenCalledWith('review', { userId: 'test-user-id' });
  });

  it('should start new workflow when existing workflow is not running', async () => {
    // Mock a non-completed request
    const mockRequest = {
      id: 'test-request-id',
      resourceType: 'Request',
      status: 'pending_review',
    };
    mockResourceService.readResource.mockResolvedValue(mockRequest);

    // Mock existing completed workflow
    const mockHandle = {
      workflowId: 'old-workflow-id',
      describe: vi.fn().mockResolvedValue({ status: { name: 'COMPLETED' } }),
    };
    mockGetPatientRequestWorkflowHandle.mockResolvedValue(mockHandle);

    // Mock new workflow creation
    const mockNewHandle = {
      workflowId: 'new-workflow-id',
      signal: vi.fn().mockResolvedValue(undefined),
    };
    mockStartPatientRequestWorkflow.mockResolvedValue(mockNewHandle);
    mockGetWorkflowId.mockReturnValue('new-workflow-id');

    const result = await signalPatientRequestWorkflowSafely(
      'test-request-id',
      'test-agency-id',
      'review',
      { userId: 'test-user-id' }
    );

    expect(result).toEqual({
      signalSent: true,
      workflowStarted: true,
      workflowId: 'new-workflow-id',
    });
    expect(mockStartPatientRequestWorkflow).toHaveBeenCalledWith('new-workflow-id', mockRequest);
    expect(mockNewHandle.signal).toHaveBeenCalledWith('review', { userId: 'test-user-id' });
  });

  it('should throw error if request not found', async () => {
    mockResourceService.readResource.mockResolvedValue(null);

    await expect(
      signalPatientRequestWorkflowSafely(
        'non-existent-request',
        'test-agency-id',
        'review',
        { userId: 'test-user-id' }
      )
    ).rejects.toThrow('Request with ID non-existent-request not found');
  });
});
