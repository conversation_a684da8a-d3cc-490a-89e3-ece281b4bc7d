import { Client, Connection, WorkflowNotFoundError } from "@temporalio/client";
import fs from "fs/promises";
import path from "path";
import { getCurrentStage } from "../config";
import { temporalQueueCurrent } from "./temporal";
import { Request } from "@hospice-os/apptypes";
import { resourceService } from "../services/resource-service";

let temporalClient: Client | null = null;

export async function getTemporalClient(): Promise<Client> {
  if (temporalClient) {
    return temporalClient;
  }

  let config = {};
  if (getCurrentStage() === "prod") {
    config = {
      address: "production.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-prod.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-prod.key"),
          ),
        },
      },
    };
  } else if (getCurrentStage() === "staging") {
    config = {
      address: "staging.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-staging.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-staging.key"),
          ),
        },
      },
    };
  } else {
    config = {
      address: "dev.w1anq.tmprl.cloud:7233",
      tls: {
        clientCertPair: {
          crt: await fs.readFile(
            path.join(__dirname, "./certificates/ca-dev.pem"),
          ),
          key: await fs.readFile(
            path.join(__dirname, "./certificates/ca-dev.key"),
          ),
        },
      },
    };
  }

  const connection = await Connection.connect(config);
  temporalClient = new Client({
    connection,
    namespace:
      getCurrentStage() === "prod"
        ? "production.w1anq"
        : getCurrentStage() === "staging"
          ? "staging.w1anq"
          : "dev.w1anq",
  });

  return temporalClient;
}

export async function startPatientRequestWorkflow(
  workflowId: string,
  request: unknown,
) {
  console.log("Starting patient workflow", workflowId, request);
  const client = await getTemporalClient();
  const handle = await client.workflow.start("patientRequestWorkflow", {
    taskQueue: temporalQueueCurrent,
    workflowId,
    args: [{ request }],
  });
  return handle;
}

export async function getPatientRequestWorkflowHandle(
  requestId: string,
  agencyId: string,
) {
  const client = await getTemporalClient();
  const id = getWorkflowId({
    prefix: "patientRequest",
    agencyId: agencyId,
    entityId: requestId,
  });
  return client.workflow.getHandle(id);
}

export async function startPdfImportWorkflow(
  requestId: string,
  agencyId: string,
) {
  const client = await getTemporalClient();
  const id = getWorkflowId({
    prefix: "patientImportPdf",
    agencyId: agencyId,
    entityId: requestId,
  });
  const handle = await client.workflow.start("patientImportPdfWorkflow", {
    taskQueue: temporalQueueCurrent,
    workflowId: id,
    args: [],
  });
  return handle;
}

export async function getPdfImportWorkflowHandle(
  workflowId: string,
  agencyId: string,
) {
  const client = await getTemporalClient();
  const id = getWorkflowId({
    prefix: "patientImportPdf",
    agencyId: agencyId,
    entityId: workflowId,
  });
  return client.workflow.getHandle(id);
}

export function getWorkflowId({
  prefix,
  agencyId,
  entityId,
}: {
  prefix: string;
  agencyId: string;
  entityId: string;
  reprocess?: boolean;
}) {
  if (!prefix || !agencyId || !entityId) {
    throw new Error("Prefix, agency ID, and entity ID are required");
  }

  return `${prefix}::${agencyId}::${entityId}`;
}

// Helper function to execute PDF import workflow with proper completion
export async function executePdfImportWorkflow(
  pdfUrl: string,
  requestId: string,
  agencyId: string,
  patientId: string,
  planOfCare?: any,
  patientChart?: any,
) {
  console.log("Starting PDF import workflow", {
    requestId,
    agencyId,
    patientId,
  });

  try {
    // Start the workflow
    const handle = await startPdfImportWorkflow(requestId, agencyId);

    // Execute the workflow steps in sequence
    console.log("Converting PDF to markdown...");
    await handle.signal("convertPDFToMarkdown", { requestId, pdfUrl });

    // Optional: Generate plan of care if provided
    if (planOfCare) {
      console.log("Generating plan of care...");
      await handle.signal("generatePlanOfCare", { planOfCare });
    }

    // Optional: Generate patient chart if provided
    if (patientChart) {
      console.log("Generating patient chart...");
      await handle.signal("generatePatientChart", { patientChart });
    }

    // Classify subdocuments
    console.log("Classifying subdocuments...");
    await handle.signal("classifySubdocuments");

    // Process subdocuments
    console.log("Processing subdocuments...");
    await handle.signal("processSubdocuments", { agencyId, patientId });

    // Create resources for subdocuments
    console.log("Creating resources for subdocuments...");
    await handle.signal("createResourcesForSubdocuments", {
      agencyId,
      patientId,
    });

    // Signal workflow completion
    console.log("Signaling workflow completion...");
    await handle.signal("completeWorkflow", { success: true });

    // Wait for the workflow to complete
    console.log("Waiting for workflow to complete...");
    await handle.result();

    console.log("PDF import workflow completed successfully");
    return { success: true, workflowId: handle.workflowId };
  } catch (error) {
    console.error("PDF import workflow failed:", error);

    // Try to signal failure to the workflow if it's still running
    try {
      const handle = await getPdfImportWorkflowHandle(requestId, agencyId);
      await handle.signal("completeWorkflow", {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    } catch (signalError) {
      console.error("Failed to signal workflow failure:", signalError);
    }

    throw error;
  }
}

/**
 * Safely send a signal to a patient request workflow, starting the workflow if it doesn't exist
 * or is not running and the request is not in completed status.
 *
 * @param requestId - The request ID
 * @param agencyId - The agency ID
 * @param signalName - The signal to send
 * @param signalArgs - The signal arguments
 * @returns Object indicating whether signal was sent or workflow was started
 */
export async function signalPatientRequestWorkflowSafely(
  requestId: string,
  agencyId: string,
  signalName: string,
  signalArgs: unknown,
): Promise<{
  signalSent: boolean;
  workflowStarted: boolean;
  workflowId?: string;
}> {
  try {
    // First, check the request status
    const request = (await resourceService.readResource(requestId)) as Request;
    if (!request || request.resourceType !== "Request") {
      throw new Error(`Request with ID ${requestId} not found`);
    }

    // If request is completed, don't send signal or start workflow
    if (request.status === "completed") {
      console.log(`Request ${requestId} is already completed, skipping signal`);
      return { signalSent: false, workflowStarted: false };
    }

    // Try to get the existing workflow handle
    let handle:
      | Awaited<ReturnType<typeof getPatientRequestWorkflowHandle>>
      | undefined;
    let workflowExists = true;

    try {
      handle = await getPatientRequestWorkflowHandle(requestId, agencyId);

      // Check if workflow is still running
      const description = await handle.describe();
      if (description.status.name !== "RUNNING") {
        console.log(
          `Workflow ${handle.workflowId} is not running (status: ${description.status.name}), starting new workflow`,
        );
        workflowExists = false;
      }
    } catch (error) {
      if (error instanceof WorkflowNotFoundError) {
        console.log(
          `Workflow not found for request ${requestId}, will start new workflow`,
        );
        workflowExists = false;
      } else {
        throw error; // Re-throw other errors
      }
    }

    if (workflowExists && handle) {
      // Workflow exists and is running, send the signal
      await handle.signal(signalName, signalArgs);
      console.log(
        `Signal ${signalName} sent to existing workflow for request ${requestId}`,
      );
      return {
        signalSent: true,
        workflowStarted: false,
        workflowId: handle.workflowId,
      };
    } else {
      // Workflow doesn't exist or isn't running, start a new one
      const workflowId = getWorkflowId({
        prefix: "patientRequest",
        agencyId: agencyId,
        entityId: requestId,
      });

      const newHandle = await startPatientRequestWorkflow(workflowId, request);
      console.log(
        `Started new workflow ${newHandle.workflowId} for request ${requestId}`,
      );

      // Wait a moment for the workflow to initialize before sending the signal
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Send the signal to the new workflow
      await newHandle.signal(signalName, signalArgs);
      console.log(
        `Signal ${signalName} sent to new workflow for request ${requestId}`,
      );

      return {
        signalSent: true,
        workflowStarted: true,
        workflowId: newHandle.workflowId,
      };
    }
  } catch (error) {
    console.error(
      `Error in signalPatientRequestWorkflowSafely for request ${requestId}:`,
      error,
    );
    throw error;
  }
}
