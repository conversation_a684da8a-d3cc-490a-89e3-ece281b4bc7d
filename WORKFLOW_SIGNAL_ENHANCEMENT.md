# Patient Request Workflow Signal Enhancement

## Overview

This enhancement implements safe signal handling for patient request workflows. When sending a signal to a `patientRequestWorkflow`, the system now automatically handles cases where:

1. The workflow is no longer running
2. The workflow never started
3. The request is already in completed status

## Key Features

### Automatic Workflow Recovery
- If a workflow is not found or not running, and the request is not completed, a new workflow is automatically started
- The signal is then sent to the new workflow
- This ensures that valid requests can always be processed, even if the original workflow was interrupted

### Request Status Validation
- Before attempting to signal or start a workflow, the system checks if the request is already completed
- Completed requests are not processed further, preventing unnecessary work

### Enhanced Error Handling
- Proper handling of `WorkflowNotFoundError` from Temporal
- Graceful fallback to starting new workflows when needed
- Comprehensive logging for debugging and monitoring

## Implementation

### New Helper Function

```typescript
export async function signalPatientRequestWorkflowSafely(
  requestId: string,
  agencyId: string,
  signalName: string,
  signalArgs: unknown,
): Promise<{
  signalSent: boolean;
  workflowStarted: boolean;
  workflowId?: string;
}>
```

### Updated API Endpoints

The following endpoints now use the safe signaling approach:

1. **POST /api/requests/:id/review** - Finalize request processing
2. **POST /api/requests/:id/modify-changeset** - Modify changeset with additional audio
3. **POST /api/requests/:id/more-audio** - Send additional audio for processing
4. **POST /api/admin/requests/:id/review** - Admin review endpoint
5. **POST /api/admin/requests/:id/process-note-schema** - Process note schema

## Usage Examples

### Before (Old Approach)
```typescript
// This would fail if workflow wasn't running
const handle = await getPatientRequestWorkflowHandle(requestId, agencyId);
const description = await handle.describe();
if (description.status.name !== "RUNNING") {
  return res.status(400).json({ error: "Workflow has already completed" });
}
await handle.signal(reviewSignal, signalArgs);
```

### After (New Approach)
```typescript
// This automatically handles workflow recovery
const result = await signalPatientRequestWorkflowSafely(
  requestId,
  agencyId,
  "review",
  signalArgs
);

if (!result.signalSent) {
  return res.status(400).json({ 
    error: "Request is already completed" 
  });
}

return res.json({
  invocationId: result.workflowId,
  workflowStarted: result.workflowStarted
});
```

## Benefits

1. **Improved Reliability**: Requests can be processed even if the original workflow was interrupted
2. **Better User Experience**: Users don't encounter cryptic workflow errors
3. **Automatic Recovery**: System self-heals from workflow interruptions
4. **Consistent Behavior**: All signal endpoints now behave consistently
5. **Enhanced Monitoring**: Better logging and return values for debugging

## Request Status Handling

The system respects the following request statuses:

- **completed**: No further processing allowed
- **pending_review**: Can be processed
- **processing**: Can be processed
- **error**: Can be processed (allows retry)
- **waiting_on_note_schema**: Can be processed

## Error Scenarios Handled

1. **Workflow Not Found**: Starts new workflow if request not completed
2. **Workflow Completed**: Starts new workflow if request not completed
3. **Workflow Failed**: Starts new workflow if request not completed
4. **Request Completed**: Skips processing entirely
5. **Request Not Found**: Returns appropriate error

## Monitoring and Logging

The implementation includes comprehensive logging:

- When workflows are started due to missing/completed workflows
- When signals are sent to existing vs new workflows
- When requests are skipped due to completion status
- Error conditions and recovery actions

This enhancement ensures robust handling of patient request workflows while maintaining backward compatibility with existing functionality.
